# Core dependencies
numpy>=1.21.0
scipy>=1.7.0

# 3D Graphics and Visualization (may have issues on Apple Silicon)
PyOpenGL>=3.1.0
PyOpenGL-accelerate>=3.1.0
glfw>=2.5.0
moderngl>=5.6.0

# Physics Simulation (install via conda for Apple Silicon)
# pybullet>=3.2.0  # Install with: conda install -c conda-forge pybullet

# Machine Learning (Apple Silicon optimized)
torch>=1.12.0
transformers>=4.20.0
stable-baselines3>=1.6.0
gymnasium>=0.26.0

# Computer Vision
opencv-python>=4.6.0
Pillow>=9.0.0

# GUI and Interface (may have compatibility issues on Apple Silicon)
matplotlib>=3.5.0

# Utilities
pyyaml>=6.0
tqdm>=4.64.0

# Apple Silicon specific notes:
# - Install PyBullet via conda: conda install -c conda-forge pybullet
# - OpenGL visualization may not work (automatically disabled on macOS)
# - Tkinter GUI may crash (automatically disabled on macOS)
# - Use command line interface instead
